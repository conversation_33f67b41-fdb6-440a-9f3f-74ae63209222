@import url("https://fonts.googleapis.com/css2?family=Inter:ital,wght@0,100..900;1,100..900&display=swap");

/* Q-Updater Dashboard Theme - Enhanced Color Scheme */
:root {
  /* Primary Colors */
  --qu-primary: #90a1b9;
  --qu-primary-hover: #45556c;
  --qu-primary-light: #e1e8f0;
  --qu-primary-dark: #1c293d;

  /* Success Colors */
  --qu-success: #10b981;
  --qu-success-hover: #059669;
  --qu-success-light: rgba(16, 185, 129, 0.1);

  /* Warning Colors */
  --qu-warning: #f59e0b;
  --qu-warning-hover: #d97706;
  --qu-warning-light: rgba(245, 158, 11, 0.1);

  /* Danger Colors */
  --qu-danger: #ef4444;
  --qu-danger-hover: #dc2626;
  --qu-danger-light: rgba(239, 68, 68, 0.1);

  /* Neutral Colors */
  --qu-border: #cad5e3;
  --qu-border-focus: #a5b4fc;
  --qu-bg-light: #f9fafb;
  --qu-bg-white: #ffffff;
  --qu-bg-dark: #111827;
  --qu-text-dark: #111827;
  --qu-text-medium: #6b7280;
  --qu-text-light: #9ca3af;

  /* Shadows */
  --qu-shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
  --qu-shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  --qu-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --qu-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --qu-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Border Radius */
  --qu-radius-sm: 4px;
  --qu-radius-md: 8px;
  --qu-radius-lg: 12px;
  --qu-radius-xl: 16px;
  --qu-radius-full: 9999px;

  /* Transitions */
  --qu-transition: all 0.2s ease;
  --qu-transition-slow: all 0.3s ease;
  --qu-transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);

  /* Layout */
  --qu-container-width: 1200px;
  --qu-sidebar-width: 280px;
  --qu-header-height: 60px;

  /* Z-index layers */
  --qu-z-dropdown: 10;
  --qu-z-sticky: 20;
  --qu-z-fixed: 30;
  --qu-z-modal: 40;
  --qu-z-tooltip: 50;

  /* Focus outline for accessibility */
  --qu-focus-ring: 0 0 0 2px var(--qu-primary-light),
    0 0 0 4px var(--qu-primary);

  /* Responsive breakpoints */
  --qu-breakpoint-sm: 576px;
  --qu-breakpoint-md: 768px;
  --qu-breakpoint-lg: 992px;
  --qu-breakpoint-xl: 1200px;
}

/* Override default WordPress admin styles */

#screen-meta-links .show-settings {
  display: none;
}

/* Base Styles */
/* Accessibility Improvements */
.qu-button,
.qu-nav-tab,
.qu-tools-nav-item,
.qu-plugin-manager-nav-item,
.qu-settings-nav-item,
input[type="text"],
input[type="email"],
input[type="password"],
input[type="url"],
input[type="number"],
select,
textarea,
button {
  transition: var(--qu-transition);
}

/* Focus states for accessibility */
.qu-button:focus,
.qu-nav-tab:focus,
.qu-tools-nav-item:focus,
.qu-plugin-manager-nav-item:focus,
.qu-settings-nav-item:focus,
input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="url"]:focus,
input[type="number"]:focus,
select:focus,
textarea:focus,
button:focus {
  outline: none;
  box-shadow: var(--qu-focus-ring);
}

/* Enhanced Skip to content link for keyboard navigation */
.qu-skip-link {
  position: absolute;
  top: -40px;
  left: 0;
  background: var(--qu-primary);
  color: white;
  padding: 10px 20px;
  z-index: var(--qu-z-fixed);
  transition: top 0.2s ease;
  text-decoration: none;
  font-weight: 500;
  border-radius: 0 0 var(--qu-radius-md) 0;
  box-shadow: var(--qu-shadow-md);
  font-size: 14px;
}

.qu-skip-link:focus {
  top: 0;
  outline: none;
  box-shadow: var(--qu-shadow-lg);
}

.qu-skip-link:hover {
  background: var(--qu-primary-hover);
  color: white;
}

/* Screen reader text - visually hidden but available to screen readers */
.qu-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

/* Focus styles for keyboard navigation */
.qu-keyboard-focus:focus-visible {
  outline: 2px solid var(--qu-primary);
  outline-offset: 2px;
  box-shadow: var(--qu-shadow-sm);
}

/* Improved contrast for better readability */
.qu-text-medium {
  color: var(--qu-text-medium);
  font-weight: 500; /* Slightly bolder for better contrast */
}

/* Animation for UI feedback */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.qu-fade-in {
  animation: fadeIn 0.3s ease forwards;
}

.qu-pulse {
  animation: pulse 1.5s ease infinite;
}

.qu-spin {
  animation: spin 1.5s linear infinite;
}

.qu-version-status {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.qu-version-status.qu-update-available {
  background-color: #fff3cd;
  color: #856404;
  border: none;
}

.qu-version-status.qu-up-to-date {
  background-color: #d4edda;
  color: #155724;
  border: none;
}

.qu-version-status.qu-error {
  background-color: #f8d7da;
  color: #721c24;
  border: none;
}

.form-table td table {
  margin-top: 10px;
}

.form-table td table th {
  font-weight: 600;
  padding: 8px;
  vertical-align: middle;
}

.form-table td table td {
  padding: 12px 8px;
}

.form-table td table small {
  color: #666;
  display: block;
  margin-top: 2px;
}

/* Add styling for the token input field */
input[type="password"].regular-text {
  width: 300px;
  max-width: 100%;
}

.form-table td a {
  color: #2271b1;
  text-decoration: none;
}

.form-table td a:hover {
  text-decoration: underline;
}

/* Token security info styling */
.qu-token-security-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 10px;
  padding: 8px 12px;
  background-color: rgba(16, 185, 129, 0.1);
  border-radius: 4px;
  color: #10b981;
  font-size: 13px;
}

.qu-token-expiration-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  padding: 8px 12px;
  background-color: rgba(245, 158, 11, 0.1);
  border-radius: 4px;
  color: #d97706;
  font-size: 13px;
}

/* Repository Management Styles */
.qu-repo-management-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  border-bottom: 1px solid var(--qu-border);
  padding-bottom: 10px;
}

.qu-repo-tab {
  padding: 8px 16px;
  border-radius: var(--qu-radius-md);
  background-color: var(--qu-bg-light);
  color: var(--qu-text-medium);
  text-decoration: none;
  font-weight: 500;
  transition: var(--qu-transition);
}

.qu-repo-tab:hover {
  background-color: var(--qu-primary-light);
  color: var(--qu-primary);
}

.qu-repo-tab.active {
  background-color: var(--qu-primary);
  color: white;
}

.qu-repo-tab-content {
  animation: fadeIn 0.3s ease;
  margin-bottom: 20px;
}

.qu-custom-repo-mappings {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 20px;
}

.qu-custom-repo-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: var(--qu-bg-light);
  border-radius: var(--qu-radius-md);
  border: 1px solid var(--qu-border);
  transition: var(--qu-transition);
}

.qu-custom-repo-item:hover {
  border-color: var(--qu-primary);
  box-shadow: var(--qu-shadow-sm);
}

.qu-custom-repo-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.qu-plugin-name {
  font-weight: 600;
  color: var(--qu-text-dark);
}

.qu-repo-type {
  display: inline-block;
  padding: 3px 8px;
  border-radius: var(--qu-radius-sm);
  font-size: 12px;
  font-weight: 500;
  background-color: var(--qu-primary-light);
  color: var(--qu-primary);
}

.qu-repo-type.gitlab {
  background-color: rgba(226, 67, 41, 0.1);
  color: #e24329;
}

.qu-repo-type.bitbucket {
  background-color: rgba(0, 82, 204, 0.1);
  color: #0052cc;
}

.qu-custom-repo-input {
  display: flex;
  align-items: center;
  gap: 10px;
}

.qu-custom-repo-badge {
  display: inline-block;
  padding: 3px 8px;
  border-radius: var(--qu-radius-sm);
  font-size: 12px;
  font-weight: 500;
  background-color: var(--qu-primary-light);
  color: var(--qu-primary);
}

.qu-custom-repo-badge.qu-default-badge {
  background-color: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.qu-custom-repo-badge.qu-detected-badge {
  background-color: rgba(245, 158, 11, 0.1);
  color: #d97706;
}

/* All Plugins Table */
.qu-filter-options {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.qu-filter-select {
  min-width: 200px;
}

.qu-plugins-table-container {
  overflow-x: auto;
  margin-bottom: 20px;
}

.qu-plugins-table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
}

.qu-plugins-table th,
.qu-plugins-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid var(--qu-border);
}

.qu-plugins-table th {
  background-color: var(--qu-bg-light);
  font-weight: 600;
  color: var(--qu-text-dark);
}

.qu-plugins-table tr:hover {
  background-color: var(--qu-bg-light);
}

.qu-plugin-name-cell {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.qu-plugin-version {
  font-size: 12px;
  color: var(--qu-text-medium);
}

.qu-plugin-type-cell {
  width: 120px;
}

.qu-plugin-type {
  display: inline-block;
  padding: 3px 8px;
  border-radius: var(--qu-radius-sm);
  font-size: 12px;
  font-weight: 500;
}

.qu-type-q-plugin {
  background-color: var(--qu-primary-light);
  color: var(--qu-primary);
}

.qu-type-github {
  background-color: rgba(36, 41, 46, 0.1);
  color: #24292e;
}

.qu-type-gitlab {
  background-color: rgba(226, 67, 41, 0.1);
  color: #e24329;
}

.qu-type-bitbucket {
  background-color: rgba(0, 82, 204, 0.1);
  color: #0052cc;
}

.qu-type-wordpress\.org {
  background-color: rgba(33, 117, 155, 0.1);
  color: #21759b;
}

.qu-plugin-repo-cell {
  width: 300px;
}

.qu-repo-input {
  width: 100%;
}

.qu-plugin-actions-cell {
  width: 120px;
  text-align: right;
}

.qu-plugin-managed {
  background-color: rgba(16, 185, 129, 0.05);
}

/* Add Custom Repository Form */
.qu-add-custom-repo-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
  padding: 20px;
  background-color: var(--qu-bg-light);
  border-radius: var(--qu-radius-md);
  border: 1px solid var(--qu-border);
  margin-bottom: 20px;
}

.qu-form-row {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.qu-form-row label {
  font-weight: 600;
  color: var(--qu-text-dark);
}

.qu-form-actions {
  margin-top: 10px;
}

/* Tools Section Styling */
.qu-tools-section {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

/* Tools Navigation */
.qu-tools-nav {
  display: flex;
  background: var(--qu-bg-white);
  border: 1px solid var(--qu-border);
  border-radius: var(--qu-radius-md);
  overflow: hidden;
  margin-bottom: 20px;
}

.qu-tools-nav-item {
  flex: 1;
  padding: 15px 20px;
  text-align: center;
  color: var(--qu-text-medium);
  font-weight: 500;
  text-decoration: none;
  border-bottom: 3px solid transparent;
  transition: var(--qu-transition);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.qu-tools-nav-item:hover {
  color: var(--qu-primary);
  background: var(--qu-primary-light);
}

.qu-tools-nav-item.active {
  color: var(--qu-primary);
  border-bottom-color: var(--qu-primary);
  background: var(--qu-primary-light);
}

.qu-tools-nav-item .dashicons {
  font-size: 18px;
  width: 18px;
  height: 18px;
}

.qu-tools-content {
  width: 100%;
}

/* Plugin Manager Section Styling */
.qu-plugin-manager-section {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

/* Plugin Manager Navigation */
.qu-plugin-manager-nav {
  display: flex;
  background: var(--qu-bg-white);
  border: 1px solid var(--qu-border);
  border-radius: var(--qu-radius-md);
  overflow: hidden;
  margin-bottom: 20px;
}

.qu-plugin-manager-nav-item {
  flex: 1;
  padding: 15px 20px;
  text-align: center;
  color: var(--qu-text-medium);
  font-weight: 500;
  text-decoration: none;
  border-bottom: 3px solid transparent;
  transition: var(--qu-transition);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.qu-plugin-manager-nav-item:hover {
  color: var(--qu-primary);
  background: var(--qu-primary-light);
}

.qu-plugin-manager-nav-item.active {
  color: var(--qu-primary);
  border-bottom-color: var(--qu-primary);
  background: var(--qu-primary-light);
}

.qu-plugin-manager-nav-item .dashicons {
  font-size: 18px;
  width: 18px;
  height: 18px;
}

.qu-plugin-manager-content {
  width: 100%;
}

/* Plugin Grid Layout */
.qu-plugins-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.qu-plugin-card {
  background: var(--qu-bg-white);
  border: 1px solid var(--qu-border);
  border-radius: var(--qu-radius-md);
  padding: 25px;
  transition: var(--qu-transition);
  display: flex;
  flex-direction: column;
  gap: 18px;
  box-shadow: var(--qu-shadow-sm);
  position: relative;
  overflow: visible; /* Changed from hidden to visible to prevent dropdown clipping */
}

.qu-plugin-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--qu-shadow-md);
  border-color: var(--qu-primary-light);
  z-index: 1; /* Add z-index to ensure dropdowns aren't covered by other cards */
}

/* Status indicator on the top right corner */
.qu-plugin-status-indicator {
  position: absolute;
  top: 0;
  right: 25px;
  width: 40px;
  height: 40px;
  border-radius: 0 0 20px 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  z-index: 1;
}

.qu-plugin-status-indicator.qu-status-up-to-date {
  background-color: var(--qu-success);
}

.qu-plugin-status-indicator.qu-status-update-available {
  background-color: var(--qu-warning);
}

.qu-plugin-status-indicator.qu-status-error {
  background-color: var(--qu-danger);
}

.qu-plugin-status-indicator .dashicons {
  font-size: 18px;
  width: 18px;
  height: 18px;
}

.qu-plugin-header {
  display: flex;
  flex-direction: column;
  gap: 12px;
  position: relative;
}

.qu-plugin-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--qu-text-dark);
}

.qu-plugin-status-badges {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.qu-plugin-meta {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.qu-plugin-repo {
  font-size: 13px;
  color: var(--qu-text-medium);
  font-family: Monaco, Consolas, monospace;
}

.qu-plugin-versions {
  display: flex;
  gap: 15px;
}

.qu-version-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.qu-version-label {
  font-size: 12px;
  color: var(--qu-text-medium);
}

.qu-plugin-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: auto;
  padding-top: 15px;
  border-top: 1px solid var(--qu-border);
}

/* Enhanced action buttons with better visual hierarchy */
.qu-plugin-action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 16px;
  border-radius: var(--qu-radius-md);
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: visible; /* Changed from hidden to visible to allow dropdown menus */
  border: none;
  outline: none;
  box-shadow: var(--qu-shadow-sm);
  text-decoration: none;
  color: var(--qu-text-dark);
  background: var(--qu-bg-light);
}

.qu-plugin-action-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--qu-shadow-md);
}

.qu-plugin-action-btn:active {
  transform: translateY(0);
}

.qu-plugin-action-btn:focus-visible {
  box-shadow: 0 0 0 2px var(--qu-primary-light), 0 0 0 4px var(--qu-primary);
}

.qu-plugin-action-btn .dashicons {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

.qu-plugin-action-btn.qu-btn-primary {
  background: var(--qu-primary);
  color: white;
}

.qu-plugin-action-btn.qu-btn-primary:hover {
  background: var(--qu-primary-hover);
}

.qu-plugin-action-btn.qu-btn-success {
  background: var(--qu-success);
  color: white;
}

.qu-plugin-action-btn.qu-btn-success:hover {
  background: var(--qu-success-hover);
}

.qu-plugin-action-btn.qu-btn-warning {
  background: var(--qu-warning);
  color: white;
}

.qu-plugin-action-btn.qu-btn-warning:hover {
  background: var(--qu-warning-hover);
}

.qu-plugin-action-btn.qu-btn-danger {
  background: var(--qu-danger);
  color: white;
}

.qu-plugin-action-btn.qu-btn-danger:hover {
  background: var(--qu-danger-hover);
}

.qu-button-large {
  padding: 14px 28px;
  font-size: 16px;
  height: auto;
  min-width: 180px;
}

/* Install Card */
.qu-install-card {
  height: 100%;
}

.qu-form-actions {
  margin-top: 30px;
  text-align: center;
}

.qu-form-actions .button-primary {
  padding: 12px 25px;
  height: auto;
  font-size: 15px;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

/* Status Messages */
.qu-install-status,
#manual-check-status {
  margin-top: 20px;
  padding: 15px;
  border-radius: var(--qu-radius-md);
  background: var(--qu-bg-light);
  border-left: 4px solid var(--qu-primary);
  display: none;
  animation: fadeIn 0.3s ease-in-out;
}

.qu-loading-indicator {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px;
  background: var(--qu-primary-light);
  border-radius: var(--qu-radius-md);
  color: var(--qu-primary);
  font-weight: 500;
}

.qu-success-state {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px;
  background: rgba(16, 185, 129, 0.1);
  border-radius: var(--qu-radius-md);
  color: var(--qu-success);
  font-weight: 500;
}

.qu-error-state {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px;
  background: rgba(239, 68, 68, 0.1);
  border-radius: var(--qu-radius-md);
  color: var(--qu-danger);
  font-weight: 500;
}

.qu-empty-state {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px;
  background: var(--qu-bg-light);
  border-radius: var(--qu-radius-md);
  color: var(--qu-text-medium);
  font-weight: 500;
}

.qu-countdown {
  margin-top: 10px;
  font-size: 13px;
  color: var(--qu-text-medium);
  text-align: center;
}

/* Animation */
.qu-spin {
  animation: spin 1.5s linear infinite;
}

.qu-install-status p {
  margin: 0;
  padding: 5px 10px;
}

.qu-text-info {
  color: #0073aa;
  background: #f3f6f8;
  border-left: 4px solid #0073aa;
}

.qu-text-success {
  color: #4ab866;
  background: #f3f6f8;
  border-left: 4px solid #4ab866;
}

.qu-text-error {
  color: #dc3232;
  background: #f3f6f8;
  border-left: 4px solid #dc3232;
}

/* Add to existing styles */
.qu-manual-actions {
  margin: 20px 0;
  padding: 20px;
  background: #fff;
  border: 1px solid #ccd0d4;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.qu-manual-actions button {
  margin-right: 10px;
}

.qu-manual-check-status,
#manual-check-status {
  margin-top: 15px;
  padding: 15px;
  border-radius: var(--qu-radius-md);
  background-color: var(--qu-bg-light);
  border-left: 4px solid var(--qu-primary);
  box-shadow: var(--qu-shadow-sm);
  animation: fadeIn 0.3s ease-in-out;
}

.qu-manual-check-status p {
  margin: 0;
  padding: 5px 10px;
}

/* Analytics Section Styling */
.qu-analytics-section {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

/* Enhanced Analytics Filters Card */
.qu-analytics-filters-card {
  background: var(--qu-bg-white);
  border: 1px solid var(--qu-border);
  border-radius: var(--qu-radius-lg);
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: var(--qu-shadow-md);
  transition: var(--qu-transition);
  position: relative;
}

.qu-analytics-filters-card:hover {
  box-shadow: var(--qu-shadow-lg);
  transform: translateY(-2px);
}

.qu-analytics-filters-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(
    to right,
    var(--qu-primary),
    var(--qu-primary-hover)
  );
  border-radius: var(--qu-radius-lg) var(--qu-radius-lg) 0 0;
}

.qu-filters-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--qu-border);
}

.qu-filters-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--qu-text-dark);
  display: flex;
  align-items: center;
  gap: 10px;
}

.qu-filters-header h3 .dashicons {
  color: var(--qu-primary);
  font-size: 20px;
  width: 20px;
  height: 20px;
}

.qu-active-filters {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.qu-active-filter-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--qu-text-medium);
}

.qu-active-filter-badge {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  background: var(--qu-primary-light);
  color: var(--qu-primary);
  border-radius: var(--qu-radius-full);
  font-size: 13px;
  font-weight: 500;
  animation: fadeIn 0.3s ease;
}

.qu-analytics-filter-form {
  width: 100%;
}

.qu-filters-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: flex-end;
}

.qu-filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 200px;
  flex: 1;
}

.qu-filter-group label {
  font-weight: 600;
  font-size: 14px;
  color: var(--qu-text-dark);
  display: flex;
  align-items: center;
  gap: 6px;
}

.qu-filter-group label .dashicons {
  color: var(--qu-primary);
  font-size: 16px;
  width: 16px;
  height: 16px;
}

.qu-filter-select {
  padding: 10px 15px;
  border: 1px solid var(--qu-border);
  border-radius: var(--qu-radius-md);
  background-color: var(--qu-bg-white);
  color: var(--qu-text-dark);
  font-size: 14px;
  transition: var(--qu-transition);
  box-shadow: var(--qu-shadow-xs);
  width: 100%;
  height: 42px;
}

.qu-filter-select:hover {
  border-color: var(--qu-primary);
  box-shadow: var(--qu-shadow-sm);
}

.qu-filter-select:focus {
  border-color: var(--qu-primary);
  box-shadow: 0 0 0 3px var(--qu-primary-light);
  outline: none;
}

.qu-date-range-container {
  display: flex;
  gap: 15px;
  flex: 2;
}

.qu-date-input {
  padding: 10px 15px;
  border: 1px solid var(--qu-border);
  border-radius: var(--qu-radius-md);
  background-color: var(--qu-bg-white);
  color: var(--qu-text-dark);
  font-size: 14px;
  transition: var(--qu-transition);
  box-shadow: var(--qu-shadow-xs);
  width: 100%;
  height: 42px;
}

.qu-date-input:hover {
  border-color: var(--qu-primary);
  box-shadow: var(--qu-shadow-sm);
}

.qu-date-input:focus {
  border-color: var(--qu-primary);
  box-shadow: 0 0 0 3px var(--qu-primary-light);
  outline: none;
}

.qu-filter-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.qu-button-text {
  background: transparent;
  color: var(--qu-primary);
  border: none;
  padding: 8px 12px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--qu-transition);
  display: inline-flex;
  align-items: center;
  gap: 6px;
  text-decoration: none;
}

.qu-button-text:hover {
  color: var(--qu-primary-hover);
  background: var(--qu-primary-light);
  border-radius: var(--qu-radius-md);
  text-decoration: none;
}

.qu-button-text .dashicons {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

.qu-analytics-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.qu-stat-card {
  background: var(--qu-bg-white);
  border-radius: var(--qu-radius-md);
  padding: 20px;
  box-shadow: var(--qu-shadow-sm);
  display: flex;
  flex-direction: column;
  gap: 10px;
  transition: var(--qu-transition);
}

.qu-stat-value {
  font-size: 28px;
  font-weight: 600;
  color: var(--qu-text-dark);
  display: flex;
  align-items: center;
  gap: 10px;
}

.qu-stat-value .dashicons {
  color: var(--qu-primary);
  font-size: 24px;
  width: 24px;
  height: 24px;
}

.qu-analytics-charts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.qu-chart-container {
  height: 300px;
  padding: 15px;
  background: var(--qu-bg-white);
  border-radius: var(--qu-radius-md);
  box-shadow: var(--qu-shadow-sm);
}

.qu-analytics-data-container {
  overflow-x: auto;
  margin-top: 15px;
}

.qu-analytics-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  border: 1px solid var(--qu-border);
  border-radius: var(--qu-radius-md);
  overflow: hidden;
}

.qu-analytics-table thead th {
  background: var(--qu-bg-light);
  padding: 12px 15px;
  text-align: left;
  font-weight: 600;
  color: var(--qu-text-dark);
  border-bottom: 2px solid var(--qu-border);
}

.qu-analytics-table tbody td {
  padding: 12px 15px;
  border-bottom: 1px solid var(--qu-border);
  color: var(--qu-text-medium);
}

.qu-analytics-table tbody tr:last-child td {
  border-bottom: none;
}

.qu-analytics-table tbody tr:hover {
  background: var(--qu-bg-light);
}

.qu-analytics-settings-form {
  display: inline-block;
}

.qu-analytics-settings {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.qu-analytics-settings .qu-checkbox-wrapper {
  margin-bottom: 15px;
}

.qu-analytics-settings .description {
  margin-top: 5px;
  color: var(--qu-text-medium);
  font-size: 13px;
}

/* Add new status styles */
.qu-plugin-status {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.qu-plugin-status.qu-status-active {
  background-color: #d4edda;
  color: #155724;
  border: none;
}

.qu-plugin-status.qu-status-inactive {
  background-color: #e2e3e5;
  color: #383d41;
  border: none;
}

/* GitHub Plugin Browser Styling */
.qu-browse-card {
  height: 100%;
}

.qu-search-form {
  margin-bottom: 20px;
}

.qu-search-input-group {
  display: flex;
  gap: 10px;
}

.qu-search-input-group input {
  flex: 1;
}

.qu-search-filters {
  display: flex;
  gap: 20px;
  margin-top: 15px;
}

.qu-filter-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.qu-select {
  padding: 8px 12px;
  border-radius: var(--qu-radius-md);
  border: 1px solid var(--qu-border);
  background-color: var(--qu-bg-white);
  color: var(--qu-text-dark);
  font-size: 14px;
  min-width: 120px;
}

.qu-search-results {
  margin-top: 20px;
}

.qu-plugin-card {
  padding: 20px;
  background: var(--qu-bg-white);
  border: 1px solid var(--qu-border);
  border-radius: var(--qu-radius-md);
  margin-bottom: 15px;
  transition: var(--qu-transition);
  display: flex;
  flex-direction: column;
  gap: 15px;
  overflow: visible; /* Ensure dropdowns aren't clipped */
}

.qu-plugin-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--qu-shadow-md);
  border-color: var(--qu-primary-light);
  z-index: 1; /* Add z-index to ensure dropdowns aren't covered by other cards */
}

.qu-plugin-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.qu-plugin-source {
  margin-right: 10px;
  color: var(--qu-text-medium);
}

.qu-plugin-source .dashicons {
  font-size: 20px;
  width: 20px;
  height: 20px;
}

.qu-plugin-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--qu-text-dark);
}

.qu-plugin-title a {
  color: var(--qu-primary);
  text-decoration: none;
}

.qu-plugin-title a:hover {
  text-decoration: underline;
}

.qu-plugin-meta {
  display: flex;
  gap: 15px;
  margin-top: 5px;
}

.qu-plugin-meta-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 13px;
  color: var(--qu-text-medium);
}

.qu-plugin-description {
  color: var(--qu-text-dark);
  font-size: 14px;
  line-height: 1.6;
  margin: 0;
}

.qu-plugin-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}

.qu-plugin-author {
  display: flex;
  align-items: center;
  gap: 10px;
}

.qu-author-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
}

.qu-author-name {
  font-size: 13px;
  color: var(--qu-text-medium);
}

.qu-author-name a {
  color: var(--qu-primary);
  text-decoration: none;
}

.qu-author-name a:hover {
  text-decoration: underline;
}

.qu-plugin-actions {
  display: flex;
  gap: 10px;
}

.qu-search-status {
  margin-top: 20px;
}

/* Rollback Card Styling */
.qu-rollback-card {
  height: 100%;
}

.qu-rollback-versions {
  margin-top: 20px;
}

.qu-rollback-version-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  margin-bottom: 10px;
  background: var(--qu-bg-light);
  border-radius: var(--qu-radius-md);
  border: 1px solid var(--qu-border);
  transition: var(--qu-transition);
}

.qu-rollback-version-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--qu-shadow-md);
  border-color: var(--qu-primary);
}

.qu-version-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.qu-version-number {
  font-weight: 600;
  color: var(--qu-text-dark);
  font-family: Monaco, Consolas, monospace;
}

.qu-version-date {
  font-size: 12px;
  color: var(--qu-text-medium);
}

.qu-version-current {
  display: inline-block;
  padding: 4px 8px;
  background: var(--qu-primary-light);
  color: var(--qu-primary);
  border-radius: var(--qu-radius-sm);
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.qu-rollback-button {
  padding: 8px 15px;
  background-color: var(--qu-warning);
  border-color: var(--qu-warning);
  color: var(--qu-bg-dark);
  font-weight: 600;
  border-radius: var(--qu-radius-md);
  transition: var(--qu-transition);
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.qu-rollback-button:hover {
  background-color: var(--qu-warning-hover);
  border-color: var(--qu-warning-hover);
  color: var(--qu-bg-dark);
  transform: translateY(-2px);
  box-shadow: var(--qu-shadow-md);
}

.qu-rollback-button .dashicons {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

.qu-rollback-warning {
  margin-top: 30px;
  padding: 15px;
  background: rgba(239, 68, 68, 0.05);
  border-radius: var(--qu-radius-md);
  border-left: 4px solid var(--qu-danger);
  display: flex;
  gap: 15px;
}

.qu-warning-icon {
  color: var(--qu-danger);
  font-size: 24px;
}

.qu-warning-icon .dashicons {
  font-size: 24px;
  width: 24px;
  height: 24px;
}

.qu-warning-content h3 {
  margin: 0 0 10px;
  color: var(--qu-danger);
  font-size: 16px;
  font-weight: 600;
  border: none;
  padding: 0;
}

.qu-warning-content p {
  margin: 0;
  color: var(--qu-text-dark);
  font-size: 13px;
  line-height: 1.6;
}

/* Add version history tooltip */
.qu-version-history {
  cursor: help;
  border-bottom: 1px dotted #666;
  position: relative;
  display: inline-block;
}

.qu-version-history:hover::after {
  content: attr(data-versions);
  position: absolute;
  background: var(--qu-bg-white);
  border: 1px solid var(--qu-border);
  padding: 10px 15px;
  border-radius: var(--qu-radius-md);
  box-shadow: var(--qu-shadow-md);
  z-index: var(--qu-z-tooltip);
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
  min-width: 200px;
  white-space: normal;
  font-size: 13px;
  line-height: 1.5;
  color: var(--qu-text-dark);
}

.qu-version-history:hover::before {
  content: "";
  position: absolute;
  top: -8px;
  left: 50%;
  transform: translateX(-50%);
  border-width: 8px;
  border-style: solid;
  border-color: var(--qu-border) transparent transparent transparent;
  z-index: var(--qu-z-tooltip);
}

.qu-plugin-actions {
  white-space: nowrap;
  display: flex;
  gap: 8px;
  align-items: center;
}

.qu-plugin-actions button {
  padding: 4px 12px;
  height: 30px;
  line-height: 28px;
}

.qu-update-plugin {
  background-color: #2271b1;
  color: #fff;
  border: none;
  font-weight: 500;
}

.qu-update-plugin:hover {
  background-color: #135e96;
}

.qu-button-danger {
  background-color: #dc3232;
  color: #fff;
  border: none;
  font-weight: 500;
}

.qu-button-danger:hover {
  background-color: #b32d2e;
}

/* Main Layout */
.qu-wrap {
  margin: 0 auto;
  min-height: calc(100vh - 32px);
  margin: 20px;
  box-sizing: border-box;
  font-family: Inter, system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
  color: var(--qu-text-dark);
  line-height: 1.6;
  letter-spacing: -0.01em;
}

#wpcontent {
  padding-left: 0px;
}

.qu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 50px;
  padding: 0px 20px;
  border-radius: var(--qu-radius-md);
  background: var(--qu-primary-dark);
  box-shadow: var(--qu-shadow-lg);
  position: relative;
  overflow: hidden;
  transition: var(--qu-transition);
  -webkit-border-radius: var(--qu-radius-md);
  -moz-border-radius: var(--qu-radius-md);
  -ms-border-radius: var(--qu-radius-md);
  -o-border-radius: var(--qu-radius-md);
}

.qu-header h1 {
  color: var(--qu-bg-white);
  font-weight: bold;
  font-size: 21px;
}

.qu-info {
  display: flex;
  justify-content: center;
  align-items: center;
}

.qu-info p {
  color: var(--qu-text-light);
  font-size: 12px;
  margin-left: 10px;
  padding-left: 10px;
  border-left: 2px solid var(--qu-text-light);
}

/* Dashboard Grid Layout */
.qu-grid {
  display: grid;
  grid-template-columns: var(--qu-sidebar-width) 1fr;
  gap: 25px;
  margin-bottom: 20px;
  align-items: start;
}

.qu-sidebar {
  background: var(--qu-bg-white);
  border: 1px solid var(--qu-border);
  border-radius: var(--qu-radius-md);
  padding: 25px;
  position: sticky;
  top: 52px;
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.qu-sidebar h2 {
  margin-top: 0;
  padding-bottom: 15px;
  border-bottom: 2px solid var(--qu-border);
  font-size: 16px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.qu-main {
  background: #fff;
  border: 1px solid #ccd0d4;
  border-radius: 4px;
  padding: 20px;
}

/* Plugin Table Improvements */
.qu-plugins-table {
  border: none;
  box-shadow: none;
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;
  background: var(--qu-bg-white);
  border-radius: var(--qu-radius-md);
  overflow: hidden;
  margin-top: 15px;
  box-shadow: none;
  border: 1px solid var(--qu-border);
}

.qu-plugins-table thead th {
  background: var(--qu-bg-light);
  border-bottom: 2px solid var(--qu-border);
  color: var(--qu-text-dark);
  font-weight: 600;
  padding: 15px;
  text-transform: uppercase;
  font-size: 12px;
  letter-spacing: 0.5px;
  white-space: nowrap;
}

.qu-plugins-table tbody tr {
  background: #fff;
  transition: background-color 0.2s ease;
}

.qu-plugins-table tbody tr:hover {
  background: #f8f9fa;
}

.qu-plugins-table td {
  padding: 15px 12px;
  vertical-align: middle;
}

.qu-plugins-table tbody td {
  border-bottom: 1px solid var(--qu-border);
  padding: 15px;
}

.qu-plugins-table tbody tr:last-child td {
  border-bottom: none;
}

/* Button Improvements */
.button,
.qu-button {
  height: 38px;
  line-height: 36px;
  padding: 0 18px;
  border-radius: var(--qu-radius-md);
  transition: var(--qu-transition-bounce);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-weight: 500;
  font-size: 14px;
  letter-spacing: 0.01em;
  box-shadow: var(--qu-shadow-xs);
  position: relative;
  overflow: hidden;
  cursor: pointer;
  border: 1px solid transparent;
}

.button:hover,
.qu-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--qu-shadow-sm);
}

.button:active,
.qu-button:active {
  transform: translateY(0);
}

.button:before,
.qu-button:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0.2)
  );
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.button:hover:before,
.qu-button:hover:before {
  transform: translateX(100%);
}

.button-primary,
.qu-button-primary {
  background: var(--qu-primary);
  border-color: var(--qu-primary);
  color: white;
  text-shadow: none;
}

.button-primary:hover,
.button-primary:focus,
.qu-button-primary:hover,
.qu-button-primary:focus {
  background: var(--qu-primary-hover);
  border-color: var(--qu-primary-hover);
  color: white;
}

.button-secondary,
.qu-button-secondary {
  background: var(--qu-bg-light);
  border-color: var(--qu-border);
  color: var(--qu-text-dark);
  text-decoration: none;
}

.button-secondary:hover,
.button-secondary:focus,
.qu-button-secondary:hover,
.qu-button-secondary:focus {
  background: var(--qu-bg-white);
  border-color: var(--qu-primary);
  color: var(--qu-primary);
}

.button .dashicons,
.qu-button .dashicons {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

/* Loading States */
.qu-loading {
  position: relative;
  opacity: 0.7;
  pointer-events: none;
}

.qu-loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid rgba(34, 113, 177, 0.1);
  border-top: 2px solid var(--qu-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Q-Updater Dashboard Theme - Additional Styles */

/* Help Tab Styling */
.contextual-help-tabs {
  background-color: var(--qu-bg-light);
  border-right: 1px solid var(--qu-border);
}

.contextual-help-tabs li {
  border-bottom: 1px solid var(--qu-border);
}

.contextual-help-tabs li a {
  padding: 12px 15px;
  font-weight: 500;
  color: var(--qu-text-dark);
  transition: var(--qu-transition);
}

.contextual-help-tabs li a:hover {
  background-color: rgba(34, 113, 177, 0.05);
  color: var(--qu-primary);
}

.contextual-help-tabs li.active {
  border-left: 4px solid var(--qu-primary);
  margin-left: -4px;
}

.contextual-help-tabs li.active a {
  background-color: var(--qu-bg-white);
  color: var(--qu-primary);
}

.help-tab-content {
  padding: 15px 20px;
}

.help-tab-content h2 {
  margin-top: 0;
  color: var(--qu-primary);
  font-size: 18px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--qu-border);
}

.help-tab-content h3 {
  margin: 20px 0 10px;
  font-size: 16px;
  color: var(--qu-text-dark);
}

.help-tab-content p {
  margin: 0 0 15px;
  line-height: 1.6;
}

.help-tab-content ul,
.help-tab-content ol {
  margin: 0 0 15px 20px;
  line-height: 1.6;
}

.help-tab-content li {
  margin-bottom: 8px;
}

.help-tab-content a {
  color: var(--qu-primary);
  text-decoration: none;
}

.help-tab-content a:hover {
  text-decoration: underline;
}

#contextual-help-back {
  background-color: var(--qu-bg-light);
  border-color: var(--qu-border);
}

.contextual-help-sidebar {
  background-color: var(--qu-bg-light);
  border-left: 1px solid var(--qu-border);
  padding: 15px;
}

.contextual-help-sidebar p {
  margin: 0 0 15px;
}

.contextual-help-sidebar a {
  display: block;
  padding: 8px 10px;
  margin-bottom: 5px;
  background-color: var(--qu-bg-white);
  border: 1px solid var(--qu-border);
  border-radius: var(--qu-radius-sm);
  color: var(--qu-primary);
  text-decoration: none;
  transition: var(--qu-transition);
}

.contextual-help-sidebar a:hover {
  background-color: var(--qu-primary);
  color: white;
  border-color: var(--qu-primary);
}

/* Stats Cards */
.qu-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin: 20px;
}

.qu-stat-card h3 {
  margin: 0 0 10px 0;
  color: var(--qu-text-medium);
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.qu-stat-card .qu-stat-value {
  font-size: 32px;
  font-weight: 600;
  color: var(--qu-text-dark);
  display: flex;
  align-items: center;
}

/* Dashboard Specific Styles */
.qu-dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: var(--qu-bg-white);
  border-radius: var(--qu-radius-lg);
  box-shadow: var(--qu-shadow-sm);
}

.qu-dashboard-title h2 {
  margin: 0 0 5px 0;
  color: var(--qu-text-dark);
  font-size: 24px;
  font-weight: 600;
}

.qu-dashboard-subtitle {
  margin: 0;
  color: var(--qu-text-medium);
  font-size: 14px;
}

.qu-dashboard-actions {
  display: flex;
  gap: 10px;
}

/* System Status */
.qu-system-status {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.qu-status-card {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  background: var(--qu-bg-white);
  border-radius: var(--qu-radius-md);
  box-shadow: var(--qu-shadow-sm);
  border-left: 4px solid var(--qu-border);
}

.qu-status-card.qu-status-healthy {
  border-left-color: var(--qu-success);
}

.qu-status-card.qu-status-error {
  border-left-color: var(--qu-error);
}

.qu-status-icon {
  margin-right: 15px;
  font-size: 24px;
}

.qu-status-healthy .qu-status-icon {
  color: var(--qu-success);
}

.qu-status-error .qu-status-icon {
  color: var(--qu-error);
}

.qu-status-info h4 {
  margin: 0 0 5px 0;
  color: var(--qu-text-dark);
  font-size: 16px;
  font-weight: 600;
}

.qu-status-info p {
  margin: 0;
  color: var(--qu-text-medium);
  font-size: 14px;
}

/* Enhanced Dashboard Stats */
.qu-dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.qu-dashboard-stats .qu-stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: var(--qu-bg-white);
  border-radius: var(--qu-radius-lg);
  box-shadow: var(--qu-shadow-sm);
  transition: var(--qu-transition);
  border-left: 4px solid var(--qu-border);
}

.qu-dashboard-stats .qu-stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--qu-shadow-md);
}

.qu-stat-card.qu-stat-primary {
  border-left-color: var(--qu-primary);
}

.qu-stat-card.qu-stat-warning {
  border-left-color: var(--qu-warning);
}

.qu-stat-card.qu-stat-success {
  border-left-color: var(--qu-success);
}

.qu-stat-card.qu-stat-info {
  border-left-color: var(--qu-info);
}

.qu-stat-icon {
  margin-right: 20px;
  font-size: 32px;
  opacity: 0.8;
}

.qu-stat-primary .qu-stat-icon {
  color: var(--qu-primary);
}

.qu-stat-warning .qu-stat-icon {
  color: var(--qu-warning);
}

.qu-stat-success .qu-stat-icon {
  color: var(--qu-success);
}

.qu-stat-info .qu-stat-icon {
  color: var(--qu-info);
}

.qu-stat-content h3 {
  margin: 0 0 5px 0;
  color: var(--qu-text-medium);
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.qu-stat-content .qu-stat-value {
  font-size: 28px;
  font-weight: 700;
  color: var(--qu-text-dark);
  margin-bottom: 5px;
}

.qu-stat-description {
  margin: 0;
  color: var(--qu-text-medium);
  font-size: 12px;
}

/* Analytics Summary Dashboard */
.qu-analytics-summary-dashboard {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.qu-analytics-summary-dashboard .qu-summary-card {
  background: var(--qu-bg-white);
  border-radius: var(--qu-radius-md);
  padding: 20px;
  box-shadow: var(--qu-shadow-sm);
  text-align: center;
  transition: var(--qu-transition);
}

.qu-analytics-summary-dashboard .qu-summary-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--qu-shadow-md);
}

.qu-analytics-summary-dashboard .qu-summary-card h4 {
  margin: 0 0 10px 0;
  color: var(--qu-text-medium);
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.qu-analytics-summary-dashboard .qu-summary-value {
  font-size: 24px;
  font-weight: 700;
  color: var(--qu-text-dark);
}

/* Dashboard Charts */
.qu-dashboard-charts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.qu-chart-card {
  background: var(--qu-bg-white);
  border-radius: var(--qu-radius-lg);
  padding: 20px;
  box-shadow: var(--qu-shadow-sm);
  transition: var(--qu-transition);
}

.qu-chart-card:hover {
  box-shadow: var(--qu-shadow-md);
}

.qu-chart-card h3 {
  margin: 0 0 20px 0;
  color: var(--qu-text-dark);
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.qu-chart-card .qu-chart-container {
  height: 300px;
  position: relative;
}

/* Dashboard Activity */
.qu-dashboard-activity {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

.qu-activity-card {
  background: var(--qu-bg-white);
  border-radius: var(--qu-radius-lg);
  padding: 20px;
  box-shadow: var(--qu-shadow-sm);
}

.qu-activity-card h3 {
  margin: 0 0 20px 0;
  color: var(--qu-text-dark);
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.qu-activity-list {
  max-height: 300px;
  overflow-y: auto;
}

.qu-activity-item {
  display: flex;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid var(--qu-border);
}

.qu-activity-item:last-child {
  border-bottom: none;
}

.qu-activity-icon {
  margin-right: 15px;
  color: var(--qu-primary);
  font-size: 20px;
}

.qu-activity-content {
  flex: 1;
}

.qu-activity-title {
  font-weight: 600;
  color: var(--qu-text-dark);
  margin-bottom: 5px;
}

.qu-activity-details {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 13px;
  color: var(--qu-text-medium);
}

.qu-activity-details .qu-version-change {
  background: var(--qu-bg-light);
  padding: 2px 8px;
  border-radius: var(--qu-radius-sm);
  font-family: monospace;
}

.qu-activity-time {
  color: var(--qu-text-light);
}

.qu-activity-empty {
  text-align: center;
  padding: 40px 20px;
  color: var(--qu-text-medium);
}

.qu-activity-empty .dashicons {
  font-size: 48px;
  margin-bottom: 15px;
  opacity: 0.5;
}

.qu-activity-actions {
  margin-top: 20px;
  text-align: center;
}

/* Quick Actions */
.qu-quick-actions {
  display: grid;
  grid-template-columns: 1fr;
  gap: 15px;
}

.qu-quick-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 15px 20px;
  background: var(--qu-bg-light);
  border: 1px solid var(--qu-border);
  border-radius: var(--qu-radius-md);
  color: var(--qu-text-dark);
  text-decoration: none;
  font-weight: 500;
  transition: var(--qu-transition);
  cursor: pointer;
}

.qu-quick-action-btn:hover {
  background: var(--qu-primary);
  color: white;
  border-color: var(--qu-primary);
  transform: translateY(-2px);
  box-shadow: var(--qu-shadow-sm);
}

.qu-quick-action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.qu-quick-action-btn:disabled:hover {
  background: var(--qu-bg-light);
  color: var(--qu-text-dark);
  border-color: var(--qu-border);
}

/* Status Badges */
.qu-version-status,
.qu-plugin-status {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: var(--qu-radius-lg);
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  min-width: 100px;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.qu-version-status::before,
.qu-plugin-status::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: currentColor;
  opacity: 0.1;
}

/* Plugin Name Styling */
.qu-plugin-name {
  font-weight: 600;
  color: var(--qu-text-dark);
  margin-bottom: 4px;
}

.qu-plugin-repo {
  color: var(--qu-text-medium);
  font-size: 12px;
}

/* Version Numbers */
.qu-version-number {
  font-family: Monaco, Consolas, monospace;
  font-size: 13px;
  color: var(--qu-text-dark);
}

/* Action Buttons */
.qu-plugin-actions {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: flex-start;
  position: relative; /* Ensure proper positioning context for dropdowns */
}

.qu-plugin-actions button {
  padding: 4px 12px;
  height: 32px;
  line-height: 30px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

/* Ensure dropdown menus in plugin actions are properly positioned */
.qu-plugin-actions .qu-dropdown-menu {
  bottom: calc(100% + 5px);
  right: 0;
  z-index: 1000; /* Ensure high z-index for visibility */
}

.qu-plugin-actions button .dashicons {
  font-size: 16px;
  width: 16px;
  height: 16px;
  margin-right: 2px;
}

/* Checkbox Styling */
.qu-plugin-checkbox {
  width: 18px;
  height: 18px;
  border-radius: 3px;
  border: 2px solid var(--qu-border);
  appearance: none;
  -webkit-appearance: none;
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;
}

.qu-plugin-checkbox:checked {
  background-color: var(--qu-primary);
  border-color: var(--qu-primary);
}

.qu-plugin-checkbox:checked::after {
  content: "✓";
  position: absolute;
  color: white;
  font-size: 12px;
  left: 3px;
  top: 0px;
}

/* Card Styles */
.qu-settings-card,
.qu-info-card,
.qu-plugins-card,
.qu-bulk-actions-card {
  background: var(--qu-bg-white);
  border-radius: var(--qu-radius-lg);
  padding: 35px;
  margin-bottom: 35px;
  box-shadow: var(--qu-shadow-md);
  border: none;
  position: relative;
  overflow: hidden;
  transition: var(--qu-transition);
}

.qu-settings-card h2,
.qu-info-card h3,
.qu-plugins-card h2,
.qu-bulk-actions-card h3 {
  margin: 0 0 28px;
  padding-bottom: 18px;
  border-bottom: 1px solid var(--qu-border);
  font-size: 20px;
  font-weight: 600;
  letter-spacing: -0.01em;
  color: var(--qu-text-dark);
  display: flex;
  align-items: center;
  gap: 12px;
}

/* Card header with buttons */
.qu-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 28px;
  padding-bottom: 18px;
  border-bottom: 1px solid var(--qu-border);
}

.qu-card-header h2 {
  margin: 0;
  padding: 0;
  border: none;
}

.qu-button-group {
  display: flex;
  gap: 10px;
  align-items: center;
}

/* Specific styling for Check for Updates and Update All buttons */
#check-all-updates,
#run-all-updates {
  min-width: 140px;
  height: 40px;
  font-weight: 600;
  border-radius: var(--qu-radius-md);
  box-shadow: var(--qu-shadow-sm);
  transition: all 0.3s ease;
}

#check-all-updates:hover,
#run-all-updates:hover {
  transform: translateY(-3px);
  box-shadow: var(--qu-shadow-md);
}

#check-all-updates:active,
#run-all-updates:active {
  transform: translateY(-1px);
}

#check-all-updates:focus,
#run-all-updates:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--qu-primary-light), 0 0 0 4px var(--qu-primary);
}

#check-all-updates .dashicons,
#run-all-updates .dashicons {
  margin-right: 5px;
}

#check-all-updates:disabled,
#run-all-updates:disabled,
.qu-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

/* Spinning animation for update icons */
.qu-spin {
  animation: spin 1.5s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.qu-settings-card h2 .dashicons,
.qu-info-card h3 .dashicons,
.qu-plugins-card h2 .dashicons,
.qu-bulk-actions-card h3 .dashicons {
  color: var(--qu-primary);
  font-size: 22px;
  width: 22px;
  height: 22px;
}

/* Settings Form Styling */
.qu-settings-form-group {
  margin-bottom: 35px;
}

.qu-settings-form-group:hover {
  box-shadow: var(--qu-shadow-md);
  transform: translateY(-3px);
  border-left-width: 6px;
}

.qu-settings-form-group:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to bottom right,
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0)
  );
  pointer-events: none;
  z-index: 1;
  border-radius: inherit;
}

.qu-settings-form-group label {
  display: block;
  margin-bottom: 15px;
  font-weight: 600;
  color: var(--qu-text-dark);
  font-size: 16px;
  letter-spacing: -0.01em;
  position: relative;
}

/* Add a subtle highlight to the label on hover */
.qu-settings-form-group:hover label {
  color: var(--qu-primary);
}

.qu-settings-form-group .qu-description {
  margin-top: 15px;
  color: var(--qu-text-medium);
  font-size: 14px;
  line-height: 1.6;
  padding: 12px 15px;
  background: var(--qu-bg-light);
  border-radius: var(--qu-radius-md);
  border-left: 3px solid var(--qu-primary-light);
}

/* Add a subtle animation for the description */
.qu-settings-form-group:hover .qu-description {
  border-left-color: var(--qu-primary);
}

/* Settings Section Styling */
.qu-settings-section {
  margin-bottom: 40px;
  position: relative;
}

.qu-settings-section h2 {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--qu-border);
  color: var(--qu-text-dark);
}

.qu-settings-section h2 .dashicons {
  color: var(--qu-primary);
  font-size: 22px;
  width: 22px;
  height: 22px;
}

/* Settings Navigation */
.qu-settings-nav {
  display: flex;
  gap: 15px;
  margin-bottom: 35px;
  padding: 5px;
  background: var(--qu-bg-light);
  border-radius: var(--qu-radius-lg);
  box-shadow: var(--qu-shadow-sm);
  overflow-x: auto;
  white-space: nowrap;
  border: 1px solid var(--qu-border);
  position: sticky;
  top: 32px;
  z-index: var(--qu-z-sticky);
}

.qu-settings-nav-item {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  padding: 12px 20px;
  border-radius: var(--qu-radius-md);
  font-size: 15px;
  font-weight: 500;
  color: var(--qu-text-medium);
  text-decoration: none;
  transition: var(--qu-transition-bounce);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.qu-settings-nav-item:hover {
  background: var(--qu-primary);
  color: var(--qu-bg-white);
}

.qu-settings-nav-item.active {
  background: var(--qu-primary);
  color: var(--qu-bg-white);
}

.qu-settings-nav-item.active:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: var(--qu-primary);
}

.qu-settings-nav-item .dashicons {
  font-size: 18px;
  width: 18px;
  height: 18px;
}

/* Settings Summary */
.qu-settings-summary {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 25px;
  margin-bottom: 35px;
}

.qu-summary-card {
  background: var(--qu-bg-white);
  border-radius: var(--qu-radius-lg);
  padding: 25px;
  border: 1px solid var(--qu-border);
  box-shadow: var(--qu-shadow-sm);
  display: flex;
  flex-direction: column;
  gap: 12px;
  transition: var(--qu-transition-bounce);
  position: relative;
  overflow: hidden;
}

.qu-summary-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--qu-shadow-md);
  border-color: var(--qu-primary);
}

.qu-summary-card:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(
    to right,
    var(--qu-primary),
    var(--qu-primary-hover)
  );
  opacity: 0;
  transition: var(--qu-transition);
}

.qu-summary-card:hover:after {
  opacity: 1;
}

.qu-summary-card-title {
  font-size: 14px;
  color: var(--qu-text-medium);
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 500;
}

.qu-summary-card-value {
  font-size: 24px;
  font-weight: 700;
  color: var(--qu-text-dark);
  display: flex;
  align-items: center;
  gap: 10px;
  line-height: 1.2;
}

.qu-summary-card-value .dashicons {
  color: var(--qu-primary);
  font-size: 20px;
  width: 20px;
  height: 20px;
}

.qu-summary-card-status {
  font-size: 13px;
  color: var(--qu-success);
  font-weight: 500;
}

.qu-summary-card-status.qu-warning {
  color: var(--qu-warning);
}

.qu-summary-card-status.qu-error {
  color: var(--qu-danger);
}

/* Input styling */
.qu-settings-form-group input[type="text"],
.qu-settings-form-group input[type="email"],
.qu-settings-form-group input[type="password"],
.qu-settings-form-group input[type="url"],
.qu-settings-form-group input[type="number"],
.qu-settings-form-group select,
.qu-settings-form-group textarea {
  width: 100%;
  padding: 14px 18px;
  border: 1px solid var(--qu-border);
  border-radius: var(--qu-radius-md);
  background-color: var(--qu-bg-white);
  color: var(--qu-text-dark);
  font-size: 15px;
  line-height: 1.5;
  transition: var(--qu-transition);
  box-shadow: var(--qu-shadow-xs);
  font-family: inherit;
}

.qu-settings-form-group input[type="text"]:hover,
.qu-settings-form-group input[type="email"]:hover,
.qu-settings-form-group input[type="password"]:hover,
.qu-settings-form-group input[type="url"]:hover,
.qu-settings-form-group input[type="number"]:hover,
.qu-settings-form-group select:hover,
.qu-settings-form-group textarea:hover {
  border-color: var(--qu-primary-hover);
  box-shadow: var(--qu-shadow-sm);
}

.qu-settings-form-group input[type="text"]:focus,
.qu-settings-form-group input[type="email"]:focus,
.qu-settings-form-group input[type="password"]:focus,
.qu-settings-form-group input[type="url"]:focus,
.qu-settings-form-group input[type="number"]:focus,
.qu-settings-form-group select:focus,
.qu-settings-form-group textarea:focus {
  border-color: var(--qu-primary);
  box-shadow: 0 0 0 3px var(--qu-primary-light);
  outline: none;
}

/* Password field styling */
.qu-settings-form-group input[type="password"] {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.qu-w3.qu-org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path fill="none" d="M0 0h24v24H0z"/><path d="M18 8h2a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V9a1 1 0 0 1 1-1h2V7a6 6 0 1 1 12 0v1zm-2 0V7a4 4 0 1 0-8 0v1h8zm-5 6v2h2v-2h-2zm-4 0v2h2v-2H7zm8 0v2h2v-2h-2z" fill="rgba(107,114,128,1)"/></svg>');
  background-repeat: no-repeat;
  background-position: right 15px center;
  background-size: 20px;
  padding-right: 45px;
}

/* Enhanced Tooltip styling with accessibility improvements */
.qu-settings-tooltip {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
  color: var(--qu-primary);
  cursor: help;
  width: 20px;
  height: 20px;
  background-color: var(--qu-primary-light);
  border-radius: var(--qu-radius-full);
  transition: var(--qu-transition);
  /* Accessibility - ensure tooltip trigger is focusable */
  outline: none;
  border: none;
}

.qu-settings-tooltip:hover,
.qu-settings-tooltip:focus {
  background-color: var(--qu-primary);
  color: white;
  transform: scale(1.1);
  box-shadow: var(--qu-shadow-sm);
}

.qu-settings-tooltip .dashicons {
  font-size: 14px;
  width: 14px;
  height: 14px;
}

.qu-tooltip-content {
  position: absolute;
  top: auto;
  bottom: 130%;
  left: 50%;
  transform: translateX(-50%) translateY(10px);
  width: 280px;
  padding: 15px;
  background: var(--qu-bg-dark);
  color: white;
  border-radius: var(--qu-radius-md);
  font-size: 13px;
  font-weight: normal;
  line-height: 1.6;
  z-index: var(--qu-z-tooltip);
  box-shadow: var(--qu-shadow-lg);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  pointer-events: none;
  text-align: left;
  /* Ensure good contrast in dark mode */
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.qu-tooltip-content::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -8px;
  border-width: 8px;
  border-style: solid;
  border-color: var(--qu-bg-dark) transparent transparent transparent;
}

.qu-settings-tooltip:hover .qu-tooltip-content,
.qu-settings-tooltip:focus .qu-tooltip-content,
.qu-settings-tooltip.qu-tooltip-active .qu-tooltip-content {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(0);
}

/* Keyboard accessibility for tooltips */
.qu-settings-tooltip:focus-visible {
  outline: 2px solid var(--qu-primary);
  outline-offset: 2px;
}

/* Tooltip with different positions */
.qu-tooltip-right .qu-tooltip-content {
  bottom: auto;
  left: 130%;
  top: 50%;
  transform: translateY(-50%) translateX(10px);
}

.qu-tooltip-right .qu-tooltip-content::after {
  top: 50%;
  left: -8px;
  margin-left: 0;
  margin-top: -8px;
  border-color: transparent var(--qu-bg-dark) transparent transparent;
}

.qu-tooltip-right:hover .qu-tooltip-content,
.qu-tooltip-right:focus .qu-tooltip-content,
.qu-tooltip-right.qu-tooltip-active .qu-tooltip-content {
  transform: translateY(-50%) translateX(0);
}

.qu-tooltip-left .qu-tooltip-content {
  bottom: auto;
  left: auto;
  right: 130%;
  top: 50%;
  transform: translateY(-50%) translateX(-10px);
}

.qu-tooltip-left .qu-tooltip-content::after {
  top: 50%;
  left: auto;
  right: -16px;
  margin-top: -8px;
  border-color: transparent transparent transparent var(--qu-bg-dark);
}

.qu-tooltip-left:hover .qu-tooltip-content,
.qu-tooltip-left:focus .qu-tooltip-content,
.qu-tooltip-left.qu-tooltip-active .qu-tooltip-content {
  transform: translateY(-50%) translateX(0);
}

/* Frequency selector styling */
.qu-frequency-selector {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 15px;
  margin-top: 15px;
}

.qu-frequency-option {
  position: relative;
  text-align: center;
}

.qu-frequency-option input[type="radio"] {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.qu-frequency-option label {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px 15px;
  background: var(--qu-bg-white);
  border: 2px solid var(--qu-border);
  border-radius: var(--qu-radius-md);
  cursor: pointer;
  transition: var(--qu-transition);
  height: 100%;
}

.qu-frequency-option input[type="radio"]:checked + label {
  background: var(--qu-primary-light);
  border-color: var(--qu-primary);
  color: var(--qu-primary);
  box-shadow: var(--qu-shadow-md);
  transform: translateY(-3px);
}

.qu-frequency-option .dashicons {
  display: block;
  margin: 0 auto 10px;
  font-size: 24px;
  width: 24px;
  height: 24px;
}

/* Checkbox styling */
.qu-settings-form-group input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.qu-settings-form-group input[type="checkbox"] + label {
  position: relative;
  padding-left: 35px;
  cursor: pointer;
  display: inline-block;
  line-height: 25px;
}

.qu-settings-form-group input[type="checkbox"] + label:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 22px;
  height: 22px;
  border: 2px solid var(--qu-border);
  background: var(--qu-bg-white);
  border-radius: var(--qu-radius-sm);
  box-sizing: border-box;
  transition: var(--qu-transition);
}

.qu-settings-form-group input[type="checkbox"]:checked + label:before {
  background: var(--qu-primary);
  border-color: var(--qu-primary);
}

.qu-settings-form-group input[type="checkbox"]:checked + label:after {
  content: "";
  position: absolute;
  left: 7px;
  top: 3px;
  width: 6px;
  height: 12px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
  box-sizing: border-box;
}

.qu-settings-form-group input[type="checkbox"]:focus + label:before {
  box-shadow: 0 0 0 3px var(--qu-primary-light);
}

/* Submit button styling */
.button-large {
  padding: 10px 25px !important;
  height: auto !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  min-width: 150px;
}

/* Settings form actions */
.qu-settings-form-actions {
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid var(--qu-border);
  text-align: center;
}

/* Settings info box */
.qu-settings-info-box {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 20px;
  background: var(--qu-bg-white);
  border-radius: var(--qu-radius-md);
  border: 1px solid var(--qu-border);
  margin-top: 10px;
}

.qu-settings-info-box .dashicons {
  color: var(--qu-primary);
  font-size: 24px;
  width: 24px;
  height: 24px;
}

.qu-settings-info-content {
  flex: 1;
}

.qu-settings-info-title {
  font-weight: 600;
  font-size: 15px;
  margin-bottom: 5px;
  color: var(--qu-text-dark);
}

.qu-settings-info-description {
  color: var(--qu-text-medium);
  font-size: 13px;
  line-height: 1.6;
}

/* Security recommendations */
.qu-security-recommendations {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 10px;
}

.qu-security-recommendation {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 20px;
  background: var(--qu-bg-white);
  border-radius: var(--qu-radius-md);
  border: 1px solid var(--qu-border);
  transition: var(--qu-transition);
}

.qu-security-recommendation.qu-completed {
  border-left: 4px solid var(--qu-success);
}

.qu-security-recommendation.qu-pending {
  border-left: 4px solid var(--qu-warning);
}

.qu-recommendation-icon .dashicons {
  font-size: 24px;
  width: 24px;
  height: 24px;
}

.qu-security-recommendation.qu-completed .qu-recommendation-icon .dashicons {
  color: var(--qu-success);
}

.qu-security-recommendation.qu-pending .qu-recommendation-icon .dashicons {
  color: var(--qu-warning);
}

.qu-recommendation-content {
  flex: 1;
}

.qu-recommendation-content h4 {
  margin: 0 0 5px 0;
  font-size: 15px;
  font-weight: 600;
  color: var(--qu-text-dark);
}

.qu-recommendation-content p {
  margin: 0;
  color: var(--qu-text-medium);
  font-size: 13px;
  line-height: 1.6;
}

/* Summary card title */
.qu-summary-card-title {
  font-size: 14px;
  color: var(--qu-text-medium);
  margin: 0;
}

.qu-settings-form-group input[type="text"]:focus,
.qu-settings-form-group input[type="email"]:focus,
.qu-settings-form-group input[type="password"]:focus,
.qu-settings-form-group input[type="url"]:focus,
.qu-settings-form-group input[type="number"]:focus,
.qu-settings-form-group select:focus,
.qu-settings-form-group textarea:focus {
  border-color: var(--qu-border-focus);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
  outline: none;
}

/* This tooltip styling has been consolidated with the earlier implementation */

/* Settings section styling */
.qu-settings-section {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--qu-border);
}

.qu-settings-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

/* Frequency selector styling */
.qu-frequency-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 15px;
}

.qu-frequency-option {
  flex: 1;
  min-width: 110px;
  text-align: center;
}

.qu-frequency-option input[type="radio"] {
  display: none;
}

.qu-frequency-option label {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px 15px;
  background: var(--qu-bg-white);
  border: 2px solid var(--qu-border);
  border-radius: var(--qu-radius-md);
  cursor: pointer;
  transition: var(--qu-transition-slow);
  box-shadow: var(--qu-shadow-sm);
  height: 100%;
  margin: 0;
}

.qu-frequency-option label:hover {
  border-color: var(--qu-primary);
  transform: translateY(-3px);
  box-shadow: var(--qu-shadow-md);
}

.qu-frequency-option input[type="radio"]:checked + label {
  background: var(--qu-primary-light);
  color: var(--qu-primary);
  border-color: var(--qu-primary);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
  transform: translateY(-3px);
}

.qu-frequency-option .dashicons {
  display: block;
  margin: 0 auto 10px;
  font-size: 24px;
  width: 24px;
  height: 24px;
  color: inherit;
}

.qu-settings-form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--qu-text-dark);
}

.qu-settings-form-group input {
  width: 100%;
  padding: 10px;
  border: 1px solid var(--qu-border);
  border-radius: var(--qu-radius-sm);
  transition: var(--qu-transition);
}

.qu-settings-form-group input:focus {
  border-color: var(--qu-primary);
  box-shadow: 0 0 0 2px rgba(34, 113, 177, 0.1);
  outline: none;
}

.qu-settings-form-group .qu-description {
  margin-top: 8px;
  font-size: 12px;
  color: var(--qu-text-medium);
}

/* Update History Styling */
.qu-update-history-list {
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  gap: 15px;
}

.qu-history-item {
  padding: 12px;
  border: 1px solid var(--qu-border);
  border-radius: var(--qu-radius-sm);
  background: var(--qu-bg-light);
  transition: var(--qu-transition);
}

.qu-history-item:hover {
  transform: translateX(5px);
  border-color: var(--qu-primary);
}

.qu-history-plugin {
  font-weight: 600;
  color: var(--qu-text-dark);
  margin-bottom: 4px;
}

.qu-history-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.qu-version-change {
  color: var(--qu-primary);
  font-family: Monaco, Consolas, monospace;
}

.qu-history-date {
  color: var(--qu-text-medium);
}

/* Bulk Actions Card */
.qu-bulk-actions-card .qu-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.qu-bulk-actions-card h3 {
  margin: 0;
  border: none;
  padding: 0;
}

#bulk-update-status {
  margin-top: 15px;
  padding: 15px;
  border-radius: var(--qu-radius-sm);
  background: var(--qu-bg-light);
}

/* Checkbox styling */
.qu-settings-form-group input[type="checkbox"] {
  display: none;
}

.qu-settings-form-group input[type="checkbox"] + label {
  position: relative;
  padding-left: 35px;
  cursor: pointer;
  display: inline-block;
  line-height: 24px;
  margin-bottom: 10px;
}

.qu-settings-form-group input[type="checkbox"] + label:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 22px;
  height: 22px;
  border: 2px solid var(--qu-border);
  background: var(--qu-bg-white);
  border-radius: var(--qu-radius-sm);
  box-sizing: border-box;
  transition: var(--qu-transition);
}

.qu-settings-form-group input[type="checkbox"]:checked + label:before {
  background-color: var(--qu-primary);
  border-color: var(--qu-primary);
}

.qu-settings-form-group input[type="checkbox"]:checked + label:after {
  content: "";
  position: absolute;
  left: 7px;
  top: 3px;
  width: 6px;
  height: 12px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
  box-sizing: border-box;
}

.qu-settings-form-group input[type="checkbox"]:focus + label:before {
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
}

/* Save Settings Button */
.qu-settings-card .submit {
  margin-top: 30px;
  margin-bottom: 0;
  padding-bottom: 0;
  text-align: center;
}

.qu-settings-card .button-primary {
  background-color: var(--qu-primary);
  border-color: var(--qu-primary);
  color: white;
  text-shadow: none;
  box-shadow: var(--qu-shadow-md);
  transition: var(--qu-transition);
  padding: 10px 25px;
  height: auto;
  font-size: 15px;
  font-weight: 600;
  letter-spacing: 0.5px;
  border-radius: var(--qu-radius-md);
  position: relative;
  overflow: hidden;
}

.qu-settings-card .button-primary:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0.2)
  );
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.qu-settings-card .button-primary:hover:before,
.qu-settings-card .button-primary:focus:before {
  transform: translateX(100%);
}

.qu-settings-card .button-primary:hover,
.qu-settings-card .button-primary:focus {
  background-color: var(--qu-primary-hover);
  border-color: var(--qu-primary-hover);
  color: white;
  transform: translateY(-2px);
  box-shadow: var(--qu-shadow-lg);
}

.qu-settings-card .button-primary:active {
  background-color: var(--qu-primary-hover);
  border-color: var(--qu-primary-hover);
  transform: translateY(0);
  box-shadow: var(--qu-shadow-sm);
}

/* Tab Navigation */
.qu-nav-tab-wrapper {
  display: flex;
  justify-content: center;
  line-height: inherit;
}

.qu-nav-tab {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  margin: 0 8px -1px 0;
  background: var(--qu-bg-light);
  color: var(--qu-text-light);
  text-decoration: none;
  white-space: nowrap;
  border-radius: var(--qu-radius-xl);
  transition: var(--qu-transition);
  -webkit-border-radius: var(--qu-radius-xl);
  -moz-border-radius: var(--qu-radius-xl);
  -ms-border-radius: var(--qu-radius-xl);
  -o-border-radius: var(--qu-radius-xl);
}

.qu-nav-tab:hover,
.qu-nav-tab:focus {
  background-color: var(--qu-bg-white);
  color: var(--qu-text-dark);
}

.qu-nav-tab-active,
.qu-nav-tab-active:focus,
.qu-nav-tab-active:focus:active,
.qu-nav-tab-active:hover {
  background: var(--qu-primary-dark);
  color: var(--qu-bg-white);
}

.qu-nav-tab .dashicons {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

/* Tab Content */
.qu-tab-content {
  border-top: none;
  border-radius: var(--qu-radius-md);
  margin-top: 20px;
  -webkit-border-radius: ;
  -moz-border-radius: ;
  -ms-border-radius: ;
  -o-border-radius: ;
}

/* Card Header */
.qu-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.qu-card-header h2 {
  margin: 0;
  padding: 0;
  border: none;
}

.qu-help-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background-color: var(--qu-primary-light);
  color: var(--qu-text-dark);
  border: none;
  border-radius: var(--qu-radius-md);
  padding: 8px 15px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--qu-transition);
}

.qu-help-button:hover {
  background-color: var(--qu-primary);
  color: white;
  transform: translateY(-2px);
  box-shadow: var(--qu-shadow-md);
}

.qu-help-button .dashicons {
  font-size: 18px;
  width: 18px;
  height: 18px;
}

/* Dropdown Menu */
.qu-dropdown {
  position: relative;
  display: inline-block;
}

.qu-dropdown-toggle {
  display: flex;
  align-items: center;
  gap: 5px;
}

.qu-dropdown-menu {
  position: absolute;
  bottom: 100%; /* Changed from top: 100% to bottom: 100% to make dropdown open upward */
  right: 0;
  z-index: var(--qu-z-modal); /* Higher z-index to ensure visibility */
  min-width: 180px;
  padding: 8px 0;
  margin: 0 0 5px; /* Changed from margin: 5px 0 0 to margin: 0 0 5px */
  background-color: var(--qu-bg-white);
  border: 1px solid var(--qu-border);
  border-radius: var(--qu-radius-md);
  box-shadow: var(--qu-shadow-md);
  display: none;
}

.qu-dropdown:hover .qu-dropdown-menu,
.qu-dropdown-toggle:focus + .qu-dropdown-menu,
.qu-dropdown-menu:hover {
  display: block;
  animation: fadeInUp 0.2s ease;
}

.qu-dropdown-item {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  padding: 8px 16px;
  clear: both;
  font-weight: 400;
  text-align: left;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
  color: var(--qu-text-dark);
  cursor: pointer;
  text-decoration: none;
}

.qu-dropdown-item:hover,
.qu-dropdown-item:focus {
  color: var(--qu-primary);
  background-color: var(--qu-primary-light);
  text-decoration: none;
}

/* Card highlight for plugins with updates */
.qu-card-highlight {
  border-color: var(--qu-warning);
  box-shadow: 0 0 0 1px var(--qu-warning), var(--qu-shadow-md);
}

/* Version indicators */
.qu-version-newer {
  color: var(--qu-warning);
  font-weight: 600;
}

/* Plugin error message */
.qu-plugin-error-message {
  margin-top: 15px;
  padding: 10px;
  background-color: var(--qu-danger-light);
  border-radius: var(--qu-radius-md);
  color: var(--qu-danger);
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Status message container */
.qu-status-message {
  margin: 15px 0;
  padding: 15px;
  border-radius: var(--qu-radius-md);
  background-color: var(--qu-bg-light);
  border-left: 4px solid var(--qu-primary);
}

/* Add animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Animation for upward dropdown */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Auto-update toggle in plugins list */
.q-updater-auto-update-toggle {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: var(--qu-radius-md);
  font-size: 12px;
  font-weight: 500;
  text-decoration: none;
  transition: var(--qu-transition);
  position: relative;
}

.q-updater-auto-update-toggle[data-enabled="1"] {
  background-color: var(--qu-success-light);
  color: var(--qu-success);
}

.q-updater-auto-update-toggle[data-enabled="0"] {
  background-color: var(--qu-primary-light);
  color: var(--qu-primary);
}

.q-updater-auto-update-toggle:hover {
  transform: translateY(-2px);
  box-shadow: var(--qu-shadow-sm);
}

.q-updater-auto-update-toggle[data-enabled="1"]:hover {
  background-color: var(--qu-success);
  color: white;
}

.q-updater-auto-update-toggle[data-enabled="0"]:hover {
  background-color: var(--qu-primary);
  color: white;
}

/* Auto-update plugins grid */
.qu-auto-update-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.qu-plugin-auto-update-item {
  background-color: var(--qu-bg-light);
  border: 1px solid var(--qu-border);
  border-radius: var(--qu-radius-md);
  padding: 15px;
  transition: var(--qu-transition);
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.qu-plugin-auto-update-item:hover {
  border-color: var(--qu-primary);
  transform: translateY(-2px);
  box-shadow: var(--qu-shadow-sm);
}

.qu-plugin-auto-update-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-left: 30px;
  font-size: 12px;
  color: var(--qu-text-medium);
}

.qu-plugin-version {
  font-family: Monaco, Consolas, monospace;
}

.qu-no-plugins-message {
  padding: 15px;
  background-color: var(--qu-bg-light);
  border-radius: var(--qu-radius-md);
  text-align: center;
  color: var(--qu-text-medium);
  font-style: italic;
}

/* Custom Repository Mappings */
.qu-custom-repo-mappings {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 15px;
}

.qu-custom-repo-item {
  background-color: var(--qu-bg-light);
  border: 1px solid var(--qu-border);
  border-radius: var(--qu-radius-md);
  padding: 15px;
  transition: var(--qu-transition);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.qu-custom-repo-item:hover {
  border-color: var(--qu-primary);
  transform: translateY(-2px);
  box-shadow: var(--qu-shadow-sm);
}

.qu-custom-repo-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.qu-plugin-name {
  font-weight: 600;
  color: var(--qu-text-dark);
}

.qu-custom-repo-input {
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
}

.qu-custom-repo-badge {
  display: inline-block;
  padding: 4px 8px;
  background-color: var(--qu-primary-light);
  color: var(--qu-primary);
  border-radius: var(--qu-radius-sm);
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Highlight effect for plugin auto-update item */
@keyframes highlight-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(67, 97, 238, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(67, 97, 238, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(67, 97, 238, 0);
  }
}

.qu-highlight-plugin {
  animation: highlight-pulse 1.5s infinite;
  border-color: var(--qu-primary) !important;
  background-color: rgba(67, 97, 238, 0.1) !important;
}

/* Reviews Section Styling */
.qu-reviews-section {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

/* Reviews Filter */
.qu-reviews-filter {
  margin-top: 20px;
  padding: 20px;
  background: var(--qu-bg-light);
  border-radius: var(--qu-radius-md);
}

.qu-filter-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 15px;
}

.qu-filter-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.qu-filter-actions {
  display: flex;
  gap: 10px;
}

/* Reviews Summary */
.qu-reviews-summary {
  margin-top: 25px;
  padding: 20px;
  background: var(--qu-bg-white);
  border: 1px solid var(--qu-border);
  border-radius: var(--qu-radius-md);
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 20px;
}

.qu-reviews-stats {
  display: flex;
  gap: 30px;
}

.qu-reviews-count {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qu-count-number {
  font-size: 36px;
  font-weight: 700;
  color: var(--qu-text-dark);
  line-height: 1;
}

.qu-count-label {
  font-size: 14px;
  color: var(--qu-text-medium);
  margin-top: 5px;
}

.qu-reviews-average {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qu-average-rating {
  display: flex;
  align-items: center;
  gap: 10px;
}

.qu-rating-number {
  font-size: 24px;
  font-weight: 700;
  color: var(--qu-text-dark);
}

.qu-average-label {
  font-size: 14px;
  color: var(--qu-text-medium);
  margin-top: 5px;
}

.qu-reviews-plugin-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.qu-reviews-plugin-info h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.qu-plugin-meta {
  display: flex;
  gap: 15px;
  color: var(--qu-text-medium);
  font-size: 14px;
}

/* Developer Email Setting */
.qu-developer-email-setting {
  margin-top: 25px;
  padding: 20px;
  background: var(--qu-bg-light);
  border-radius: var(--qu-radius-md);
}

.qu-email-input-group {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

/* Write Review Form */
.qu-write-review-form {
  margin-top: 20px;
}

/* Star Rating */
.qu-star-rating {
  display: flex;
  gap: 5px;
}

.qu-star {
  color: #d1d5db;
  cursor: default;
}

.qu-star-filled {
  color: #f59e0b;
}

.qu-star-rating-input .qu-star {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.qu-star-rating-input .qu-star:hover {
  transform: scale(1.2);
}

/* Reviews List */
.qu-reviews-list {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.qu-review-item {
  padding: 20px;
  background: var(--qu-bg-white);
  border: 1px solid var(--qu-border);
  border-radius: var(--qu-radius-md);
  box-shadow: var(--qu-shadow-sm);
}

.qu-review-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.qu-review-meta {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.qu-reviewer-name {
  font-weight: 600;
  font-size: 16px;
}

.qu-review-date {
  font-size: 13px;
  color: var(--qu-text-medium);
}

.qu-review-plugin {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  color: var(--qu-primary);
  background: var(--qu-primary-light);
  padding: 5px 10px;
  border-radius: var(--qu-radius-sm);
  margin-bottom: 15px;
  display: inline-flex;
}

.qu-review-content {
  font-size: 15px;
  line-height: 1.6;
  color: var(--qu-text-dark);
}

.qu-review-actions {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.qu-settings-card {
  animation: fadeIn 0.5s ease-out;
}

.qu-summary-card:nth-child(1) {
  animation: fadeIn 0.3s ease-out;
}

.qu-summary-card:nth-child(2) {
  animation: fadeIn 0.4s ease-out;
}

.qu-summary-card:nth-child(3) {
  animation: fadeIn 0.5s ease-out;
}

.button-primary:hover {
  animation: pulse 1s infinite;
}

/* Add focus styles for accessibility */
.button:focus,
input:focus,
select:focus,
textarea:focus,
.qu-settings-nav-item:focus {
  outline: 2px solid var(--qu-primary);
  outline-offset: 2px;
}

/* Rollback Version Item */
.qu-rollback-version-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border: 1px solid var(--qu-border);
  border-radius: var(--qu-radius-md);
  margin-bottom: 10px;
  background: var(--qu-bg-white);
  transition: var(--qu-transition);
}

.qu-rollback-version-item:hover {
  border-color: var(--qu-primary-light);
  box-shadow: var(--qu-shadow-sm);
}

.qu-version-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.qu-version-number {
  font-size: 16px;
  font-weight: 600;
  color: var(--qu-text-dark);
  display: flex;
  align-items: center;
  gap: 10px;
}

.qu-version-current {
  display: inline-block;
  padding: 3px 8px;
  background: var(--qu-success-light);
  color: var(--qu-success);
  border-radius: var(--qu-radius-sm);
  font-size: 12px;
  font-weight: 500;
}

.qu-version-date {
  font-size: 13px;
  color: var(--qu-text-medium);
}

/* Backup Item */
.qu-backup-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border: 1px solid var(--qu-border);
  border-radius: var(--qu-radius-md);
  margin-bottom: 10px;
  background: var(--qu-bg-white);
  transition: var(--qu-transition);
}

.qu-backup-item:hover {
  border-color: var(--qu-primary-light);
  box-shadow: var(--qu-shadow-sm);
}

.qu-backup-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.qu-backup-version {
  font-size: 16px;
  font-weight: 600;
  color: var(--qu-text-dark);
  display: flex;
  align-items: center;
  gap: 10px;
}

.qu-backup-date {
  font-size: 13px;
  color: var(--qu-text-medium);
}

.qu-backup-size {
  font-size: 12px;
  color: var(--qu-text-light);
  font-family: Monaco, Consolas, monospace;
}

.qu-restore-button {
  background-color: var(--qu-warning);
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: var(--qu-radius-sm);
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: var(--qu-transition);
}

.qu-restore-button:hover {
  background-color: var(--qu-warning-hover);
}

/* Dashboard Responsive Design */
@media (max-width: 768px) {
  .qu-dashboard-header {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .qu-dashboard-actions {
    justify-content: center;
  }

  .qu-dashboard-stats {
    grid-template-columns: 1fr;
  }

  .qu-dashboard-charts {
    grid-template-columns: 1fr;
  }

  .qu-dashboard-activity {
    grid-template-columns: 1fr;
  }

  .qu-analytics-summary-dashboard {
    grid-template-columns: repeat(2, 1fr);
  }

  .qu-system-status {
    grid-template-columns: 1fr;
  }

  .qu-chart-card .qu-chart-container {
    height: 250px;
  }
}

@media (max-width: 480px) {
  .qu-analytics-summary-dashboard {
    grid-template-columns: 1fr;
  }

  .qu-dashboard-stats .qu-stat-card {
    padding: 15px;
  }

  .qu-stat-content .qu-stat-value {
    font-size: 24px;
  }

  .qu-stat-icon {
    font-size: 28px;
  }

  .qu-chart-card .qu-chart-container {
    height: 200px;
  }

  .qu-quick-actions {
    gap: 10px;
  }

  .qu-quick-action-btn {
    padding: 12px 15px;
    font-size: 14px;
  }
}
